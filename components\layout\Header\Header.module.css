/* Header.module.css */

/* Main aurora background animation for the navbar */
@keyframes aurora {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.aurora-border {
  position: relative;
  overflow: hidden;
  --aurora-speed: 5s;
  --aurora-size: 200px;
}

.aurora-border::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  background-image: conic-gradient(
    from 90deg at 50% 50%,
    rgba(255, 60, 0, 0.5) 0%,
    rgba(255, 60, 0, 0.5) 15%,
    rgba(244, 63, 94, 0.5) 30%,
    transparent 50%,
    rgba(255, 60, 0, 0.5) 100%
  );
  animation: rotate-gradient 6s linear infinite;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.aurora-border:hover::before {
  opacity: 1;
}

@keyframes rotate-gradient {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Dropdown animation */
@keyframes slide-in-down {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.dropdown-enter {
  animation: slide-in-down 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

/* Full-screen mobile menu animations */
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.mobile-menu-enter {
  animation: fade-in 0.3s ease-out;
}

@keyframes slide-in-from-right {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.nav-item-animate {
  animation: slide-in-from-right 0.5s ease-out backwards;
}
