// TypingPlayground.hooks.ts
import { useCallback, useEffect, useRef, useState } from 'react';
import {
  fetchTextFromApi,
  generateApiPrompt,
  initialApiKeyError as globalApiKeyError,
} from './TypingPlayground.api';
import { FALLBACK_TEXT, LANGUAGES_FOR_DROPDOWN, TextCategory } from './TypingPlayground.config';

const debounce = <F extends (...args: any[]) => any>(func: F, delay: number) => {
  let timeoutId: NodeJS.Timeout;
  return (...args: Parameters<F>): Promise<ReturnType<F>> => {
    return new Promise(resolve => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        resolve(func(...args));
      }, delay);
    });
  };
};

export interface TypingTestResults {
  wpm: number;
  accuracy: number;
  time: number;
  mistakes: number;
}

export const useTypingPlayground = () => {
  const [isClient, setIsClient] = useState(false);
  const [currentText, setCurrentText] = useState<string>('');
  const [userInput, setUserInput] = useState<string>('');
  const [startTime, setStartTime] = useState<number | null>(null);
  const [timeElapsed, setTimeElapsed] = useState<number>(0);
  const [mistakes, setMistakes] = useState<number>(0);
  const [wpm, setWpm] = useState<number>(0);
  const [accuracy, setAccuracy] = useState<number>(100);
  const [testActive, setTestActive] = useState<boolean>(false);

  // Initial states to match the image
  const [currentLanguage, setCurrentLanguage] = useState<'english' | 'other'>('english');
  const [selectedActualOtherLang, setSelectedActualOtherLang] = useState<string>(
    LANGUAGES_FOR_DROPDOWN[0],
  );
  const [isLangDropdownOpen, setIsLangDropdownOpen] = useState<boolean>(false);

  const [testMode, setTestMode] = useState<'time' | 'words' | 'quote' | 'custom'>('time');
  const [testTimeOption, setTestTimeOption] = useState<number>(30); // Matches image if time mode
  const [wordCountOption, setWordCountOption] = useState<number>(25);

  const [showPunctuation, setShowPunctuation] = useState<boolean>(true); // Punctuation active in image
  const [showNumbers, setShowNumbers] = useState<boolean>(false); // Numbers inactive in image

  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [apiKeyError, setApiKeyError] = useState<boolean>(globalApiKeyError);
  const [rateLimitError, setRateLimitError] = useState<string | null>(null);

  const [showResultModal, setShowResultModal] = useState<boolean>(false);
  const [finalResults, setFinalResults] = useState<TypingTestResults | null>(null);

  const inputRef = useRef<HTMLTextAreaElement>(null);
  const timerIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const langDropdownRef = useRef<HTMLDivElement>(null);

  const [selectedCategory, setSelectedCategory] = useState<TextCategory>('general');

  useEffect(() => {
    setIsClient(true);
  }, []);

  const promptGenerator = useCallback(() => {
    return generateApiPrompt(selectedCategory);
  }, [selectedCategory]);

  const fetchAndResetText = useCallback(
    async (isFullReset = true) => {
      setIsLoading(true);
      setRateLimitError(null);
      if (isFullReset) {
        setTestActive(false);
        setUserInput('');
        setWpm(0);
        setAccuracy(100);
        setTimeElapsed(0);
        setMistakes(0);
        setShowResultModal(false);
        setFinalResults(null);
        setStartTime(null);
        if (timerIntervalRef.current) clearInterval(timerIntervalRef.current);
      }

      const {
        text,
        error,
        isRateLimitError: apiRateLimitError,
        isApiKeyError: apiFetchSpecificError,
      } = await fetchTextFromApi(promptGenerator, selectedCategory);

      setCurrentText(text);

      if (apiFetchSpecificError) {
        setApiKeyError(true);
      } else if (globalApiKeyError && text === FALLBACK_TEXT && !error) {
        setApiKeyError(true);
      } else {
        setApiKeyError(false);
      }

      if (apiRateLimitError) {
        setRateLimitError(error || 'Rate limit exceeded.');
      }

      setIsLoading(false);
    },
    [promptGenerator, selectedCategory, showResultModal], // Added selectedCategory to dependencies
  );

  // Debounced version for option changes (always full reset)
  const debouncedFetchAfterOptionChange = useCallback(
    debounce(() => fetchAndResetText(true), 300), // Reduced debounce time for better responsiveness
    [fetchAndResetText],
  );

  const handleOptionChange = (changeFn: () => void) => {
    // Apply the state change immediately
    changeFn();

    // Reset the test state immediately
    setTestActive(false);
    setUserInput('');
    setWpm(0);
    setAccuracy(100);
    setTimeElapsed(0);
    setMistakes(0);
    setShowResultModal(false);
    setFinalResults(null);
    setStartTime(null);
    if (timerIntervalRef.current) clearInterval(timerIntervalRef.current);

    // Show loading state immediately
    setIsLoading(true);

    // Debounce the actual text fetching
    debouncedFetchAfterOptionChange();
  };

  useEffect(() => {
    fetchAndResetText(true); // Initial fetch on mount
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Runs once on mount

  const completeTest = useCallback(() => {
    if (!testActive) return;

    setTestActive(false);
    if (timerIntervalRef.current) clearInterval(timerIntervalRef.current);

    const finalTypedChars = userInput.length;
    let correctChars = 0;
    for (let i = 0; i < finalTypedChars; i++) {
      if (i < currentText.length && userInput[i] === currentText[i]) {
        correctChars++;
      }
    }

    const finalMistakes = finalTypedChars - correctChars;
    const finalAccuracy =
      finalTypedChars > 0
        ? Math.round((correctChars / finalTypedChars) * 100)
        : currentText.length === 0
        ? 100
        : 0;

    // WPM calculation: (correct chars / 5) / time_in_minutes
    // This is a common way to calculate WPM, treating a "word" as 5 characters.
    const wordsCounted = correctChars / 5;
    const finalMinutes = timeElapsed / 60;
    const finalWpm = finalMinutes > 0 ? Math.round(wordsCounted / finalMinutes) : 0;

    setFinalResults({
      wpm: finalWpm,
      accuracy: finalAccuracy,
      time: timeElapsed,
      mistakes: finalMistakes,
    });
    setShowResultModal(true);
  }, [userInput, timeElapsed, currentText, testActive]);

  useEffect(() => {
    if (testActive) {
      if (!startTime) {
        setStartTime(Date.now() - timeElapsed * 1000);
      }
      timerIntervalRef.current = setInterval(() => {
        const now = Date.now();
        const elapsed = (now - (startTime || now)) / 1000;
        setTimeElapsed(elapsed);

        if (testMode === 'time' && elapsed >= testTimeOption) {
          setTimeElapsed(testTimeOption); // Ensure time doesn't exceed option
          completeTest();
        }
      }, 100);
    } else if (timerIntervalRef.current) {
      clearInterval(timerIntervalRef.current);
    }
    return () => {
      if (timerIntervalRef.current) clearInterval(timerIntervalRef.current);
    };
  }, [testActive, testMode, testTimeOption, startTime, completeTest]);

  useEffect(() => {
    if (!currentText) return;

    if (!testActive && userInput.length === 0) {
      setWpm(0);
      setAccuracy(100);
      setMistakes(0);
      return;
    }

    const typedChars = userInput.length;
    if (typedChars === 0) return; // No calculations if no input

    let currentMistakesCount = 0;
    let currentCorrectChars = 0;
    for (let i = 0; i < typedChars; i++) {
      if (i < currentText.length) {
        if (userInput[i] === currentText[i]) {
          currentCorrectChars++;
        } else {
          currentMistakesCount++;
        }
      } else {
        // User typed past the end of currentText (should ideally not happen with input length constraint)
        currentMistakesCount++;
      }
    }
    setMistakes(currentMistakesCount);

    const currentAccuracy =
      typedChars > 0 ? Math.max(0, Math.round((currentCorrectChars / typedChars) * 100)) : 100;
    setAccuracy(currentAccuracy);

    if (timeElapsed > 0) {
      const wordsSoFar = currentCorrectChars / 5;
      const minutesSoFar = timeElapsed / 60;
      setWpm(Math.round(wordsSoFar / minutesSoFar));
    } else {
      setWpm(0);
    }
  }, [userInput, timeElapsed, currentText, testActive]);

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    if (showResultModal || isLoading || (!currentText && !apiKeyError && !rateLimitError)) return;
    const newTypedText = e.target.value;

    if (!testActive && newTypedText.length > 0 && currentText) {
      setTestActive(true);
      setStartTime(Date.now());
      setTimeElapsed(0); // Reset time elapsed
      setMistakes(0);
    }

    if (newTypedText.length <= currentText.length || currentText === FALLBACK_TEXT || apiKeyError) {
      setUserInput(newTypedText);
    } else if (newTypedText.length > currentText.length && currentText.length > 0) {
      // If user tries to type past the end of non-fallback text, complete the test
      setUserInput(currentText); // Set input to current text to avoid overtyping
      completeTest();
      return;
    }

    if (
      !showResultModal &&
      currentText.length > 0 &&
      currentText !== FALLBACK_TEXT &&
      !apiKeyError
    ) {
      if (newTypedText.length === currentText.length) {
        // Completed the whole text
        completeTest();
      } else if (testMode === 'words') {
        // For word mode, check if typed word count met and last char is space
        const typedWords = newTypedText.trim().split(/\s+/).filter(Boolean);
        if (typedWords.length >= wordCountOption && newTypedText.endsWith(' ')) {
          // To accurately complete, trim the input to the word count
          const actualInputForWordMode =
            newTypedText.trim().split(/\s+/).slice(0, wordCountOption).join(' ') + ' ';
          setUserInput(actualInputForWordMode); // Adjust user input
          // Slight delay to allow state to update before completing
          setTimeout(() => completeTest(), 0);
        }
      }
    }
  };

  const handleLanguageButtonClick = (langType: 'english' | 'other') => {
    const prevLang = currentLanguage;
    const prevOtherLang = selectedActualOtherLang;

    if (langType === 'other') {
      setIsLangDropdownOpen(prev => !prev);
      if (prevLang !== 'other') {
        setCurrentLanguage('other');
        // Fetch will occur if selectedActualOtherLang leads to a different prompt
        // Or if the user selects a new language from the dropdown
        // No immediate fetch here, let dropdown or option change handle it
      }
    } else {
      // English
      setIsLangDropdownOpen(false);
      if (prevLang !== 'english') {
        setCurrentLanguage('english');
        fetchAndResetText(true);
      } else {
        // If already English, refresh the text
        fetchAndResetText(true);
      }
    }
  };

  const handleDropdownLangSelect = (language: string) => {
    setSelectedActualOtherLang(language);
    setIsLangDropdownOpen(false);
    setCurrentLanguage('other'); // Ensure mode is 'other'
    fetchAndResetText(true); // Fetch new text for the selected other language
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (langDropdownRef.current && !langDropdownRef.current.contains(event.target as Node)) {
        setIsLangDropdownOpen(false);
      }
    };
    if (isClient) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    return () => {
      if (isClient) {
        document.removeEventListener('mousedown', handleClickOutside);
      }
    };
  }, [isClient]);

  return {
    isClient,
    currentText,
    userInput,
    timeElapsed,
    wpm,
    accuracy,
    mistakes,
    testActive,
    currentLanguage,
    selectedActualOtherLang,
    isLangDropdownOpen,
    testMode,
    testTimeOption,
    wordCountOption,
    showPunctuation,
    showNumbers,
    isLoading,
    rateLimitError,
    apiKeyError,
    showResultModal,
    setShowResultModal,
    finalResults,
    selectedCategory,
    inputRef,
    langDropdownRef,
    setTestMode,
    setTestTimeOption,
    setWordCountOption,
    setShowPunctuation,
    setShowNumbers,
    setSelectedCategory,
    handleInputChange,
    handleOptionChange,
    handleLanguageButtonClick,
    handleDropdownLangSelect,
    fetchAndResetText,
  };
};
